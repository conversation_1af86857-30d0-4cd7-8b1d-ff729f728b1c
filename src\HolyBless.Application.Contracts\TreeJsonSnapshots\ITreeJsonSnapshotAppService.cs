using System.Threading.Tasks;
using HolyBless.Enums;
using Volo.Abp.Application.Services;

namespace HolyBless.TreeJsonSnapshots
{
    public interface ITreeJsonSnapshotAppService : IApplicationService
    {
        Task<string> GetTreeJsonAsync(TreeType treeType, string languageCode, int? rootId = null);

        Task RefreshTreeJsonAsync(TreeType treeType, string languageCode, int? rootId = null);

        Task UpdateTreeJsonAsync(TreeType treeType, string languageCode, int? rootId, string treeJsonData);

        Task ClearCacheAsync(TreeType? treeType = null, int? rootId = null, string? languageCode = null);
    }
}
