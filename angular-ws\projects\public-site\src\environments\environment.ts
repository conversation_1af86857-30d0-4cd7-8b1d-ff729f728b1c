 import { Environment } from '../abp';

const baseUrl = 'http://localhost:4200';

const oAuthConfig = {
  issuer: 'https://localhost:44362/',//'https://localhost:44362/',
  redirectUri: baseUrl,
  clientId: 'holybless_App_Public', //If run against local BE, remove _Public
  responseType: 'code',
  scope: 'offline_access holybless',
  requireHttps: true,
};

export const environment = {
  production: false,
  application: {
    baseUrl,
    name: 'holybless',
  },
  oAuthConfig,
  apis: {
    default: {
      url: 'https://localhost:44362',  //can not have ending /
      rootNamespace: 'Holybless',
    },
    AbpAccountPublic: {
      url: oAuthConfig.issuer,
      rootNamespace: 'AbpAccountPublic',
    },
  },
} as Environment;
