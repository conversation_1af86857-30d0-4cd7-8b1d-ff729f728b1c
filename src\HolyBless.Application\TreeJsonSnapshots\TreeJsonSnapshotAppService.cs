using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Enums;
using HolyBless.Buckets;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using HolyBless.Configs;

namespace HolyBless.TreeJsonSnapshots
{
    [AllowAnonymous]
    public class TreeJsonSnapshotAppService : HolyBlessAppService, HolyBless.TreeJsonSnapshots.ITreeJsonSnapshotAppService
    {
        private readonly IRepository<TreeJsonSnapshot, int> _treeRepository;
        private readonly IMemoryCache _cache;
        private readonly AppConfig _appConfig;

        // Track cache keys so we can clear selectively
        private static readonly ConcurrentDictionary<string, byte> _cacheKeys = new();

        private readonly MemoryCacheEntryOptions _cacheOptions = new MemoryCacheEntryOptions
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
            SlidingExpiration = TimeSpan.FromMinutes(10)
        };

        public TreeJsonSnapshotAppService(
            IRepository<TreeJsonSnapshot, int> treeRepository,
            IMemoryCache cache,
            AppConfig appConfig,
            ICachedFileUrlAppService cachedFileUrlAppService
            ) : base(cachedFileUrlAppService)
        {
            _treeRepository = treeRepository;
            _cache = cache;
            _appConfig = appConfig;
        }

        private static string GetCacheKey(TreeType treeType, int? rootId, string languageCode)
        {
            var rootPart = rootId.HasValue ? rootId.Value.ToString() : "null";
            return $"TreeJson:{treeType}:{rootPart}:{languageCode}";
        }

        /// <summary>
        /// Get cached tree JSON for given tree type and root id. If not present in cache, load from DB and cache it.
        /// </summary>
        public async Task<string> GetTreeJsonAsync(TreeType treeType, string languageCode, int? rootId = null)
        {
            if (!_appConfig.EnableCache)
            {
                return "";
            }

            // Use provided languageCode or default to SimplifiedChinese
            languageCode ??= LangCode.SimplifiedChinese;
            var key = GetCacheKey(treeType, rootId, languageCode);
            if (_cache.TryGetValue<string>(key, out var json))
            {
                return json ?? string.Empty;
            }

            // Load from DB by matching TreeType, RootId, and LanguageCode (RootId may be null)
            var queryable = await _treeRepository.GetQueryableAsync();
            var snapshot = await queryable
                .Where(x => x.TreeType == treeType && x.RootId == rootId && x.LanguageCode == languageCode)
                .FirstOrDefaultAsync();

            json = snapshot?.TreeJsonData ?? string.Empty;

            _cache.Set(key, json, _cacheOptions);
            _cacheKeys.TryAdd(key, 0);

            return json;
        }

        /// <summary>
        /// Force refresh the cache for a specific treeType/rootId by reloading from DB.
        /// </summary>
        public async Task RefreshTreeJsonAsync(TreeType treeType, string languageCode, int? rootId = null)
        {
            // Use provided languageCode or default to SimplifiedChinese
            languageCode ??= LangCode.SimplifiedChinese;
            var key = GetCacheKey(treeType, rootId, languageCode);
            var queryable = await _treeRepository.GetQueryableAsync();
            var snapshot = await queryable
                .Where(x => x.TreeType == treeType && x.RootId == rootId && x.LanguageCode == languageCode)
                .FirstOrDefaultAsync();

            var json = snapshot?.TreeJsonData ?? string.Empty;
            _cache.Set(key, json, _cacheOptions);
            _cacheKeys.TryAdd(key, 0);
        }

        /// <summary>
        /// Insert or update a TreeJsonSnapshot in the database and update the in-memory cache.
        /// </summary>
        public async Task UpdateTreeJsonAsync(TreeType treeType, string languageCode, int? rootId, string treeJsonData)
        {
            if (!_appConfig.EnableCache)
            {
                return;
            }
            // Normalize null
            treeJsonData ??= string.Empty;
            languageCode ??= LangCode.SimplifiedChinese;

            var queryable = await _treeRepository.GetQueryableAsync();
            var existing = await queryable
                .Where(x => x.TreeType == treeType && x.RootId == rootId && x.LanguageCode == languageCode)
                .FirstOrDefaultAsync();

            if (existing == null)
            {
                var entity = new TreeJsonSnapshot
                {
                    TreeType = treeType,
                    RootId = rootId,
                    LanguageCode = languageCode,
                    TreeJsonData = treeJsonData
                };
                await _treeRepository.InsertAsync(entity, true);
            }
            else
            {
                existing.TreeJsonData = treeJsonData;
                await _treeRepository.UpdateAsync(existing, true);
            }

            // update cache
            var key = GetCacheKey(treeType, rootId, languageCode);
            _cache.Set(key, treeJsonData, _cacheOptions);
            _cacheKeys.TryAdd(key, 0);
        }

        /// <summary>
        /// Clear cache entries. When all parameters are null, clears all tracked tree JSON entries.
        /// When treeType is provided and others are null, clears all entries for the treeType.
        /// When treeType and rootId are provided, clears entries for that combination.
        /// When languageCode is also provided, clears only that specific entry.
        /// </summary>
        public async Task ClearCacheAsync(TreeType? treeType = null, int? rootId = null, string? languageCode = null)
        {
            if (!_appConfig.EnableCache)
            {
                return;
            }

            if (treeType == null && rootId == null && languageCode == null)
            {
                // Clear all tracked keys
                foreach (var k in _cacheKeys.Keys.ToList())
                {
                    _cache.Remove(k);
                    _cacheKeys.TryRemove(k, out _);
                }

                //Admin site, clear all DB entries as well
                if (_appConfig.ExposeWritableApi)
                {
                    // If writable APIs are enabled, also clear all TreeJsonSnapshot entries from DB
                    await _treeRepository.DeleteAsync(x => true);
                }
                return;
            }

            if (treeType != null && rootId == null && languageCode == null)
            {
                var prefix = $"TreeJson:{treeType.Value}:";
                var keys = _cacheKeys.Keys.Where(k => k.StartsWith(prefix, StringComparison.OrdinalIgnoreCase)).ToList();
                foreach (var k in keys)
                {
                    _cache.Remove(k);
                    _cacheKeys.TryRemove(k, out _);
                }
                if (_appConfig.ExposeWritableApi)
                {
                    // If writable APIs are enabled, also clear all DB entries for this treeType
                    await _treeRepository.DeleteAsync(x => x.TreeType == treeType.Value);
                }
                return;
            }

            if (treeType != null && rootId != null && languageCode != null)
            {
                // Clear specific entry for exact treeType/rootId/languageCode
                var singleKey = GetCacheKey(treeType.Value, rootId, languageCode);
                _cache.Remove(singleKey);
                _cacheKeys.TryRemove(singleKey, out _);

                if (_appConfig.ExposeWritableApi)
                {
                    // If writable APIs are enabled, also clear the specific DB entry
                    await _treeRepository.DeleteAsync(x => x.TreeType == treeType.Value && x.RootId == rootId && x.LanguageCode == languageCode);
                }
                return;
            }

            if (treeType != null && rootId != null)
            {
                // Clear all languages for this specific treeType/rootId combination
                var keyPrefix = $"TreeJson:{treeType.Value}:{rootId?.ToString() ?? "null"}:";
                var keysToRemove = _cacheKeys.Keys.Where(k => k.StartsWith(keyPrefix, StringComparison.OrdinalIgnoreCase)).ToList();
                foreach (var k in keysToRemove)
                {
                    _cache.Remove(k);
                    _cacheKeys.TryRemove(k, out _);
                }

                if (_appConfig.ExposeWritableApi)
                {
                    // If writable APIs are enabled, also clear all DB entries for this treeType/rootId (all languages)
                    await _treeRepository.DeleteAsync(x => x.TreeType == treeType.Value && x.RootId == rootId);
                }
            }
        }
    }
}