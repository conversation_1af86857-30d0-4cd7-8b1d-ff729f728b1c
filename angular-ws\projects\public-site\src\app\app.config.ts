import {
  ApplicationConfig,
  provideZoneChangeDetection,
  isDevMode,
  inject,
  provideAppInitializer,
  //importProvidersFrom,
} from '@angular/core';
import { provideServiceWorker } from '@angular/service-worker';
import {
  //HTTP_INTERCEPTORS,
  //HttpClient,
  provideHttpClient,
  withFetch,
  withInterceptors,
} from '@angular/common/http';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import {
  provideRouter,
  //Router,
  withEnabledBlockingInitialNavigation,
  withInMemoryScrolling,
} from '@angular/router';
//import Aura from '@primeng/themes/aura';
import { providePrimeNG } from 'primeng/config';
//import { HttpHandler } from '@angular/common/http';
import { appRoutes } from '@/app.routes';
import { CustomAuraPreset } from './theme.config';
////import { provideAbpCore, withOptions } from '@abp/ng.core';
//import { provideAbpOAuth } from '@abp/ng.oauth';
//import { registerLocaleData } from '@angular/common';
//import zh from '@angular/common/locales/zh';
//import en from '@angular/common/locales/en';
import { RequestHeaderInterceptor } from './interceptors/request-header.interceptor';
import { environment } from '../environments/environment';
import { provideAbpCore } from '../abp';
import { EnvironmentService } from '../abp/services/RestService';

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(
      appRoutes,
      withInMemoryScrolling({
        anchorScrolling: 'enabled',
        scrollPositionRestoration: 'enabled',
      }),
      withEnabledBlockingInitialNavigation(),
    ),
    provideAnimationsAsync(),
    provideHttpClient(withFetch(), withInterceptors([RequestHeaderInterceptor])),
    providePrimeNG({
      theme: {
        preset: CustomAuraPreset,
        options: {
          darkModeSelector: '.app-dark',
        },
      },
    }),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    // 替代APP_INITIALIZER的初始化
    // DynamicRouteService,
    provideAppInitializer(() => {
      // Initialize environment service with environment data
      const envService = inject(EnvironmentService);
      envService.setState(environment);
    }),
    // Local ABP providers
    ...provideAbpCore({
      environment,
      registerLocaleFn: () => Promise.resolve(null),
      sendNullsAsQueryParam: false,
    }),
  ],
};
