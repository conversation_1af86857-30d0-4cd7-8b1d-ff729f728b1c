/**
 * ABP Core providers and injection tokens for local implementation
 * Based on @abp/ng.core version 9.2.4
 */

import { InjectionToken, makeEnvironmentProviders, Provider } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ABP } from '../models/Common';
import { 
  RestService, 
  EnvironmentService, 
  HttpErrorReporterService, 
  ExternalHttpClient,
  CORE_OPTIONS as SERVICE_CORE_OPTIONS
} from '../services/RestService';

// Use the same injection token from services
export const CORE_OPTIONS = SERVICE_CORE_OPTIONS;

// Options factory
export function coreOptionsFactory(options: Partial<ABP.Root> = {}): ABP.Root {
  return {
    environment: {},
    registerLocaleFn: () => Promise.resolve(null),
    skipGetAppConfiguration: false,
    skipInitAuthService: false,
    sendNullsAsQueryParam: false,
    tenantKey: undefined,
    ...options,
  };
}

// Providers
export function provideAbpCore(options: Partial<ABP.Root> = {}): Provider[] {
  return [
    {
      provide: CORE_OPTIONS,
      useFactory: () => coreOptionsFactory(options),
    },
    EnvironmentService,
    HttpErrorReporterService,
    ExternalHttpClient,
    {
      provide: RestService,
      useFactory: (
        coreOptions: ABP.Root,
        http: HttpClient,
        externalHttp: ExternalHttpClient,
        environment: EnvironmentService,
        httpErrorReporter: HttpErrorReporterService
      ) => new RestService(coreOptions, http, externalHttp, environment, httpErrorReporter),
      deps: [CORE_OPTIONS, HttpClient, ExternalHttpClient, EnvironmentService, HttpErrorReporterService]
    }
  ];
}

// Make environment providers for standalone applications
export function provideAbpCoreEnvironment(options: Partial<ABP.Root> = {}) {
  return makeEnvironmentProviders([
    ...provideAbpCore(options)
  ]);
}