{"App": {"SelfUrl": "https://localhost:44362", "AngularUrl": "http://localhost:4200", "CorsOrigins": "https://*.HolyBless.com,http://localhost:4200", "RedirectAllowedUrls": "http://localhost:4200", "DisablePII": false, "HealthCheckUrl": "/health-status"}, "ConnectionStrings": {"Default": "Host=localhost;Port=5432;Database=Holybless;User ID=postgres;Password=***********;Ssl Mode=Disable;Trust Server Certificate=true;"}, "AuthServer": {"Authority": "https://localhost:44362", "RequireHttpsMetadata": true, "SwaggerClientId": "<PERSON><PERSON><PERSON>_Swagger", "CertificatePassPhrase": "34a74220-d0f5-44dd-8895-4c3e0d5a5fae"}, "StringEncryption": {"DefaultPassPhrase": "YGVQoCMR9EJKx7Gm"}, "AppConfigs": {"Environment": "<PERSON>", "ExposeWritableApi": false, "EnableCache": true, "EnableBackgroundWorkers": false}}