/**
 * Local copy of ABP Rest API interfaces to avoid dependency on @abp/ng.core
 * Based on @abp/ng.core version 9.2.4
 */

import { HttpClient, HttpRequest, HttpHeaders, HttpParameterCodec, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ABP } from './Common';

// Rest API related types
export declare namespace Rest {
  type Config = Partial<{
    apiName: string;
    skipHandleError: boolean;
    skipAddingHeader: boolean;
    observe: Observe;
    httpParamEncoder?: HttpParameterCodec;
  }>;

  const enum Observe {
    Body = "body",
    Events = "events",
    Response = "response"
  }

  const enum ResponseType {
    ArrayBuffer = "arraybuffer",
    Blob = "blob",
    JSON = "json",
    Text = "text"
  }

  type Params = HttpParams | {
    [param: string]: any;
  };

  interface Request<T> {
    body?: T;
    headers?: HttpHeaders | {
      [header: string]: string | string[];
    };
    method: string;
    params?: Params;
    reportProgress?: boolean;
    responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';
    url: string;
    withCredentials?: boolean;
  }
}

// RestService is now implemented in ../services/RestService.ts