/**
 * Local copy of ABP RestService implementation to avoid dependency on @abp/ng.core
 * Based on @abp/ng.core version 9.2.4
 */

import { Injectable, Inject } from '@angular/core';
import { HttpClient, HttpParams, HttpContextToken, HttpContext } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject, Subject } from 'rxjs';
import { catchError, map, distinctUntilChanged, filter } from 'rxjs/operators';
import { ABP } from '../models/Common';
import { Rest } from '../models/Rest';

// Create injection token for ABP Root options
import { InjectionToken } from '@angular/core';
export const CORE_OPTIONS = new InjectionToken<ABP.Root>('CORE_OPTIONS');

// Utility functions
export function isUndefinedOrEmptyString(value: any): boolean {
  return value === undefined || value === '';
}

export function isNullOrUndefined(obj: any): boolean {
  return obj === null || obj === undefined;
}

export function exists(value: any): boolean {
  return !isNullOrUndefined(value);
}

export function isObjectAndNotArrayNotNode(obj: any): boolean {
  return typeof obj === 'object' && obj !== null && !Array.isArray(obj) && typeof obj.nodeType === 'undefined';
}

export function deepMerge(target: any, source: any): any {
  if (isObjectAndNotArrayNotNode(target) && isObjectAndNotArrayNotNode(source)) {
    return deepMergeRecursively(target, source);
  } else if (isNullOrUndefined(target) && isNullOrUndefined(source)) {
    return {};
  } else {
    return exists(source) ? source : target;
  }
}

function deepMergeRecursively(target: any, source: any): any {
  const shouldNotRecurse = isNullOrUndefined(target) || 
    isNullOrUndefined(source) || 
    Array.isArray(target) || 
    Array.isArray(source);
  
  if (shouldNotRecurse) {
    return exists(source) ? source : target;
  }

  const result = { ...target };
  
  Object.keys(source).forEach(key => {
    if (isObjectAndNotArrayNotNode(source[key])) {
      result[key] = deepMerge(target[key], source[key]);
    } else {
      result[key] = source[key];
    }
  });

  return result;
}

// Internal Store for state management
export class InternalStore<T = any> {
  private state$ = new BehaviorSubject<T>(this.initialState);
  private update$ = new Subject<T>();

  get state(): T {
    return this.state$.value;
  }

  sliceState = <R>(selector: (state: T) => R, compareFn: (a: R, b: R) => boolean = (a, b) => a === b) => 
    this.state$.pipe(
      map(selector),
      distinctUntilChanged(compareFn)
    );

  sliceUpdate = <R>(selector: (state: T) => R, filterFn: (value: R) => boolean = (x) => x !== undefined) =>
    this.update$.pipe(
      map(selector),
      filter(filterFn)
    );

  constructor(private initialState: T) {}

  patch(state: Partial<T> | T): void {
    let patchedState = state as T;
    if (typeof state === 'object' && !Array.isArray(state)) {
      patchedState = { ...this.state, ...state };
    }
    this.state$.next(patchedState);
    this.update$.next(patchedState);
  }

  deepPatch(state: Partial<T>): void {
    this.state$.next(deepMerge(this.state, state));
    this.update$.next(state as T);
  }

  set(state: T): void {
    this.state$.next(state);
    this.update$.next(state);
  }

  reset(): void {
    this.set(this.initialState);
  }
}

// Helper function for API URL mapping
const mapToApiUrl = (key?: string) => (apis: any) => 
  ((key && apis[key]) || apis.default).url || apis.default.url;

const mapToIssuer = (issuer?: string): string => {
  if (!issuer) return '';
  return issuer.endsWith('/') ? issuer : issuer + '/';
};

// Environment Service
@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {
  private store = new InternalStore<any>({});

  get createOnUpdateStream() {
    return this.store.sliceUpdate;
  }

  getEnvironment$(): Observable<any> {
    return this.store.sliceState(state => state);
  }

  getEnvironment(): any {
    return this.store.state;
  }

  getApiUrl(key?: string): string {
    return mapToApiUrl(key)(this.store.state?.apis);
  }

  getApiUrl$(key?: string): Observable<string> {
    return this.store.sliceState(state => state.apis).pipe(map(mapToApiUrl(key)));
  }

  setState(environment: any): void {
    this.store.set(environment);
  }

  getIssuer(): string {
    const issuer = this.store.state?.oAuthConfig?.issuer;
    return mapToIssuer(issuer);
  }

  getIssuer$(): Observable<string> {
    return this.store.sliceState(state => state?.oAuthConfig?.issuer).pipe(map(mapToIssuer));
  }

  getImpersonation(): any {
    return this.store.state?.oAuthConfig?.impersonation || {};
  }

  getImpersonation$(): Observable<any> {
    return this.store.sliceState(state => state?.oAuthConfig?.impersonation || {});
  }
}

// HTTP Error Reporter Service
@Injectable({
  providedIn: 'root'
})
export class HttpErrorReporterService {
  private _reporter$ = new Subject<any>();
  private _errors$ = new BehaviorSubject<any[]>([]);

  get reporter$(): Observable<any> {
    return this._reporter$.asObservable();
  }

  get errors$(): Observable<any[]> {
    return this._errors$.asObservable();
  }

  get errors(): any[] {
    return this._errors$.value;
  }

  reportError(error: any): void {
    this._reporter$.next(error);
    this._errors$.next([...this.errors, error]);
  }
}

// External HTTP Client
export const IS_EXTERNAL_REQUEST = new HttpContextToken(() => false);

@Injectable({
  providedIn: 'root'
})
export class ExternalHttpClient extends HttpClient {
  override request(first: any, url?: any, options: any = {}): Observable<any> {
    if (typeof first === 'string') {
      this.setPlaceholderContext(options);
      return super.request(first, url || '', options);
    }
    this.setPlaceholderContext(first);
    return super.request(first);
  }

  private setPlaceholderContext(optionsOrRequest: any): void {
    optionsOrRequest.context ??= new HttpContext();
    optionsOrRequest.context.set(IS_EXTERNAL_REQUEST, true);
  }
}

// Main RestService implementation
@Injectable()
export class RestService {
  constructor(
    @Inject(CORE_OPTIONS) private options: ABP.Root,
    private http: HttpClient,
    private externalHttp: ExternalHttpClient,
    private environment: EnvironmentService,
    private httpErrorReporter: HttpErrorReporterService
  ) {}

  private getApiFromStore(apiName?: string): string {
    return this.environment.getApiUrl(apiName);
  }

  handleError(err: any): Observable<any> {
    this.httpErrorReporter.reportError(err);
    return throwError(() => err);
  }

  request<T, R>(
    request: any | Rest.Request<T>, 
    config?: Rest.Config, 
    api?: string
  ): Observable<R> {
    config = config || {};
    api = api || this.getApiFromStore(config.apiName);
    
    const { method, params, ...options } = request;
    const { observe = "body", skipHandleError } = config;
    const url = this.removeDuplicateSlashes(api + request.url);
    const httpClient = this.getHttpClient(config.skipAddingHeader);
    
    return httpClient
      .request(method, url, {
        observe,
        ...(params && {
          params: this.getParams(params, config.httpParamEncoder),
        }),
        ...options,
      })
      .pipe(
        catchError(err => 
          skipHandleError ? throwError(() => err) : this.handleError(err)
        )
      );
  }

  private getHttpClient(isExternal?: boolean): HttpClient {
    return isExternal ? this.externalHttp : this.http;
  }

  private getParams(params: any, encoder?: any): HttpParams {
    const filteredParams = Object.entries(params).reduce((acc: any, [key, value]) => {
      if (isUndefinedOrEmptyString(value)) return acc;
      if (value === null && !this.options.sendNullsAsQueryParam) return acc;
      acc[key] = value;
      return acc;
    }, {});

    return encoder
      ? new HttpParams({ encoder, fromObject: filteredParams })
      : new HttpParams({ fromObject: filteredParams });
  }

  private removeDuplicateSlashes(url: string): string {
    return url.replace(/([^:]\/)\/+/g, '$1');
  }
}